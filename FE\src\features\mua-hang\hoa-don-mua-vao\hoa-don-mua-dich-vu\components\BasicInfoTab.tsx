import { useState, useContext } from 'react';
import { useWatch } from 'react-hook-form';
import {
  MaNCCSearchColBasicInfo,
  MaNVSearchColBasicInfo,
  TKCoSearchColBasicInfo,
  MaCTSearchColBasicInfo
} from '../cols-definition';
import BasicInfoTabType1 from '@/components/cac-loai-form/popup-form-type-1/BasicInfoTabType1';
import BasicInfoTabType2 from '@/components/cac-loai-form/popup-form-type-2/BasicInfoTabType2';
import TaxCodePopUpForm from '@/components/cac-loai-form/add-tax-code-popup/TaxCodePopUpForm';
import { PaymentTermSelectField } from '@/components/custom/arito/form/search-fields';
import { Type1Tabs } from '@/components/cac-loai-form/popup-form-type-1/Tabs';
import { Type2Tabs } from '@/components/cac-loai-form/popup-form-type-2/Tabs';
import { RadioButton } from '@/components/custom/arito/form/radio-button';
import { AritoFormContext } from '@/components/custom/arito/form/form';
import { FormField } from '@/components/custom/arito/form/form-field';
import { AritoIcon } from '@/components/custom/arito';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import {
  currencyOptions,
  transactionTypeOptions
} from '../schemas';
interface Props {
  formMode: 'add' | 'edit' | 'view';
  showTaxCodePopupForm: boolean;
  setShowTaxCodePopupForm: (value: boolean) => void;
}
export const BasicInfoTab = ({ formMode, setShowTaxCodePopupForm, showTaxCodePopupForm }: Props) => {
  const [formType, setFormType] = useState<'chung_tu' | 'hoa_don' | 'phieu_nhap'>('chung_tu');
  const { control } = useContext(AritoFormContext);

  const isPaymentChecked = useWatch({
    control,
    name: 'payment',
    defaultValue: false
  });

  const isDiscountChecked = useWatch({
    control,
    name: 'discount',
    defaultValue: false
  });

  return (
    <div className='p-4'>
      <div className='grid grid-cols-1 gap-x-8 lg:grid-cols-2'>
        {/* Cột trái - Thông tin cơ bản */}
        <div className='space-y-4'>
          {/* Giao dịch */}
          <div className='flex items-center gap-4'>
            <Label className='w-32 text-sm font-medium'>Giao dịch</Label>
            <div className='flex-1'>
              <RadioButton
                name='dataType'
                options={transactionTypeOptions}
                defaultValue='service'
                orientation='horizontal'
                onChange={() => {}}
                className='gap-6'
              />
            </div>
          </div>

          {/* Chi tiền và Chiết khấu */}
          <div className='grid grid-cols-1 gap-4 lg:grid-cols-2'>
            <div className='flex items-center gap-4'>
              <div className='flex items-center gap-2' style={{ width: '160px' }}>
                <FormField type='checkbox' name='payment' disabled={formMode === 'view'} />
                {!isPaymentChecked && <label className='text-sm font-medium'>Chi tiền</label>}
              </div>
              {isPaymentChecked && (
                <FormField
                  type='select'
                  name='paymentMethod'
                  className='w-40'
                  options={[
                    { value: 'cash', label: 'Tiền mặt' },
                    { value: 'bank', label: 'Chuyển khoản' },
                    { value: 'credit', label: 'Khác' }
                  ]}
                  disabled={formMode === 'view'}
                />
              )}
            </div>

            <div className='flex items-center gap-4'>
              <div className='flex items-center gap-2' style={{ width: '160px' }}>
                <FormField type='checkbox' name='discount' disabled={formMode === 'view'} />
                <Label className='text-sm font-medium'>Chiết khấu</Label>
              </div>

              {isDiscountChecked && (
                <div className='flex items-center gap-2'>
                  <FormField
                    type='select'
                    name='discountType'
                    className='w-48'
                    options={[
                      { value: 'byHand', label: 'Tự nhập' },
                      { value: 'percent', label: 'Giảm % theo hóa đơn' },
                      { value: 'fixed', label: 'Giảm tiền trên tổng hóa đơn' }
                    ]}
                    disabled={formMode === 'view'}
                  />
                  <FormField
                    type='text'
                    name='discountValue'
                    className='w-16'
                    disabled={formMode === 'view'}
                  />
                </div>
              )}
            </div>
          </div>
          {/* Mã nhà cung cấp */}
          <FormField
            className='grid grid-cols-[160px,1fr] items-center gap-2'
            type='text'
            label='Mã nhà cung cấp'
            name='supplierCode'
            disabled={formMode === 'view'}
            withSearch={true}
            searchEndpoint='accounting/supplier'
            searchColumns={MaNCCSearchColBasicInfo}
            actionButtons={['add', 'edit']}
            headerFields={<BasicInfoTabType1 formMode={formMode} />}
            tabs={Type1Tabs({ formMode })}
            placeholder='Nhập và tra cứu'
          />

          {/* Tên nhà cung cấp */}
          <FormField
            className='grid grid-cols-[160px,1fr] items-center gap-2'
            type='text'
            label='Tên nhà cung cấp'
            name='supplierName'
            disabled={formMode === 'view'}
            placeholder='Tên nhà cung cấp/đơn vị'
            defaultValue='Tên nhà cung cấp/đơn vị'
          />

          {/* Địa chỉ */}
          <FormField
            className='grid grid-cols-[160px,1fr] items-center gap-2'
            label='Địa chỉ'
            name='address'
            type='text'
            disabled={formMode === 'view'}
            placeholder='Địa chỉ nhà cung cấp'
            defaultValue='Địa chỉ nhà cung cấp'
          />

          {/* Mã nhân viên */}
          <FormField
            className='grid grid-cols-[160px,1fr] items-center gap-2'
            label='Mã nhân viên'
            name='employeeCode'
            type='text'
            disabled={formMode === 'view'}
            withSearch
            searchColumns={MaNVSearchColBasicInfo}
            searchEndpoint='hr/employee'
            placeholder='sdfsd'
            defaultValue='DSFSDFSD'
          />

          {/* Tài khoản có */}
          <FormField
            className='grid grid-cols-[160px,1fr] items-center gap-2'
            label='Tài khoản có'
            name='accountNumber'
            type='text'
            disabled={formMode === 'view'}
            withSearch
            searchEndpoint='accounting/account'
            searchColumns={TKCoSearchColBasicInfo}
            actionButtons={['add', 'edit']}
            headerFields={<BasicInfoTabType2 formMode={formMode} />}
            tabs={Type2Tabs({ formMode })}
          />

          {/* Diễn giải */}
          <FormField
            className='grid grid-cols-[160px,1fr] items-center gap-2'
            label='Diễn giải'
            name='description'
            type='text'
            disabled={formMode === 'view'}
          />

          {/* Mã số thuế */}
          <div className='grid grid-cols-[160px,1fr] items-center gap-2'>
            <Label className='text-sm font-medium'>Mã số thuế</Label>
            <div className='flex items-center gap-2'>
              <FormField
                type='text'
                name='taxCode'
                disabled={formMode === 'view'}
                className='flex-1'
              />
              <div className='cursor-pointer'>
                <AritoIcon icon={15} />
              </div>
              <div className='cursor-pointer' onClick={() => setShowTaxCodePopupForm(true)}>
                <AritoIcon icon={58} />
              </div>
            </div>
          </div>

          {/* Dự công nợ */}
          <FormField
            className='grid grid-cols-[160px,1fr] items-center gap-2'
            label='Dự công nợ'
            type='number'
            name='debt'
            disabled={formMode === 'view'}
            defaultValue='0'
            inputClassName='text-red-600'
          />

          {/* Người giao hàng */}
          <FormField
            className='grid grid-cols-[160px,1fr] items-center gap-2'
            label='Người giao hàng'
            type='text'
            name='deliveryPerson'
            disabled={formMode === 'view'}
            placeholder='Nhập tên người giao hàng'
          />

          {/* Email */}
          <FormField
            className='grid grid-cols-[160px,1fr] items-center gap-2'
            label='Email'
            name='email'
            type='text'
            disabled={formMode === 'view'}
            placeholder='Email'
          />

          {/* Hạn thanh toán */}
          <PaymentTermSelectField
            name='paymentTerm'
            label='Hạn thanh toán'
            formMode={formMode === 'view' ? 'view' : formMode === 'edit' ? 'edit' : 'add'}
            labelClassName='w-40'
            inputClassName='flex-1'
            className='grid grid-cols-[160px,1fr] items-center gap-2'
          />
        </div>

        {/* Cột phải - Thông tin chứng từ và trạng thái */}
        <div className='space-y-4'>
          {/* Số chứng từ */}
          <FormField
            className='grid grid-cols-[120px,1fr] items-center gap-2'
            label='Số chứng từ'
            withSearch={true}
            type='text'
            name='bookNumber'
            disabled={formMode === 'view'}
            searchEndpoint='accounting/transaction'
            searchColumns={MaCTSearchColBasicInfo}
            defaultValue='MDV06.25.000001'
          />

          {/* Ngày chứng từ */}
          <FormField
            label='Ngày chứng từ'
            className='grid grid-cols-[120px,1fr] items-center gap-2'
            type='date'
            name='documentDate'
            disabled={formMode === 'view'}
            defaultValue='2025-06-06'
          />

          {/* Số hóa đơn */}
          <FormField
            label='Số hóa đơn'
            className='grid grid-cols-[120px,1fr] items-center gap-2'
            type='text'
            name='invoiceNumber'
            disabled={formMode === 'view'}
            placeholder='SỐ HÓA ĐƠN'
          />

          {/* Ký hiệu */}
          <FormField
            label='Ký hiệu'
            className='grid grid-cols-[120px,1fr] items-center gap-2'
            type='text'
            name='invoiceSymbol'
            disabled={formMode === 'view'}
            placeholder='KÝ HIỆU'
          />

          {/* Ngày hóa đơn */}
          <FormField
            label='Ngày hóa đơn'
            className='grid grid-cols-[120px,1fr] items-center gap-2'
            type='date'
            name='invoiceDate'
            disabled={formMode === 'view'}
            defaultValue='2025-06-06'
          />

          {/* Ngoại tệ và tỷ giá */}
          <div className='grid grid-cols-[120px,1fr] items-center gap-2'>
            <Label className='text-sm font-medium'>Ngoại tệ</Label>
            <div className='flex items-center gap-2'>
              <FormField
                name='foreignCurrency'
                type='select'
                disabled={formMode === 'view'}
                className='w-20'
                defaultValue='VND'
                options={currencyOptions}
              />
              <FormField
                name='exchangeRate'
                type='text'
                disabled={formMode === 'view'}
                className='flex-1'
                defaultValue='1.0000'
              />
            </div>
          </div>

          {/* Trạng thái */}
          <div className='grid grid-cols-[120px,1fr] items-center gap-2'>
            <Label className='text-sm font-medium'>Trạng thái</Label>
            <div className='flex items-center gap-2'>
              <span className='text-sm'>Đã ghi sổ</span>
              <Switch
                defaultChecked={true}
                disabled={formMode === 'view'}
              />
            </div>
          </div>

          {/* Dữ liệu được nhận */}
          <div className='grid grid-cols-[120px,1fr] items-center gap-2'>
            <Label className='text-sm font-medium'>Dữ liệu được nhận</Label>
            <FormField
              name='dataReceived'
              type='checkbox'
              disabled={formMode === 'view'}
            />
          </div>
        </div>
      </div>

      {showTaxCodePopupForm && (
        <TaxCodePopUpForm
          showTaxCodePopupForm={showTaxCodePopupForm}
          setShowTaxCodePopupForm={setShowTaxCodePopupForm}
          formMode={formMode}
          currentObj={{}}
        />
      )}
    </div>
  );
};
