import * as yup from 'yup';

// Form validation schema
export const purchaseServiceInvoiceSchema = yup
  .object({
    // Transaction type
    dataType: yup.string().default('service'),
    
    // Checkboxes
    payment: yup.boolean().default(false),
    discount: yup.boolean().default(false),
    
    // Supplier information
    supplierCode: yup.string().default(''),
    supplierName: yup.string().default('Tên nhà cung cấp/đơn vị'),
    address: yup.string().default('Địa chỉ nhà cung cấp'),
    taxCode: yup.string().default(''),
    
    // Employee and account
    employeeCode: yup.string().default('DSFSDFSD'),
    accountNumber: yup.string().default(''),
    description: yup.string().default(''),
    
    // Financial information
    debt: yup.number().default(0),
    deliveryPerson: yup.string().default(''),
    email: yup.string().email().default(''),
    paymentTerm: yup.string().default(''),
    
    // Document information
    bookNumber: yup.string().default('MDV06.25.000001'),
    documentDate: yup.date().default(() => new Date('2025-06-06')),
    invoiceNumber: yup.string().default(''),
    invoiceSymbol: yup.string().default(''),
    invoiceDate: yup.date().default(() => new Date('2025-06-06')),
    
    // Currency and exchange
    foreignCurrency: yup.string().default('VND'),
    exchangeRate: yup.string().default('1.0000'),
    
    // Status
    status: yup.string().default('posted'),
    dataReceived: yup.boolean().default(false),
  })
  .required();

// Form data type
export type PurchaseServiceInvoiceFormData = yup.InferType<typeof purchaseServiceInvoiceSchema>;

// Initial values for the form
export const purchaseServiceInvoiceInitialValues: PurchaseServiceInvoiceFormData = {
  dataType: 'service',
  payment: false,
  discount: false,
  supplierCode: '',
  supplierName: 'Tên nhà cung cấp/đơn vị',
  address: 'Địa chỉ nhà cung cấp',
  taxCode: '',
  employeeCode: 'DSFSDFSD',
  accountNumber: '',
  description: '',
  debt: 0,
  deliveryPerson: '',
  email: '',
  paymentTerm: '',
  bookNumber: 'MDV06.25.000001',
  documentDate: new Date('2025-06-06'),
  invoiceNumber: '',
  invoiceSymbol: '',
  invoiceDate: new Date('2025-06-06'),
  foreignCurrency: 'VND',
  exchangeRate: '1.0000',
  status: 'posted',
  dataReceived: false,
};

// Currency options
export const currencyOptions = [
  { value: 'VND', label: 'VND' },
  { value: 'USD', label: 'USD' },
  { value: 'EUR', label: 'EUR' },
  { value: 'JPY', label: 'JPY' },
];

// Transaction type options
export const transactionTypeOptions = [
  { value: 'service', label: 'DV. Mua dịch vụ' },
  { value: 'asset', label: 'TS. Mua tài sản/công cụ' },
];

// Status options
export const statusOptions = [
  { value: 'posted', label: 'Đã ghi sổ' },
  { value: 'pending', label: 'Chờ duyệt' },
  { value: 'unposted', label: 'Chưa ghi sổ' },
];
