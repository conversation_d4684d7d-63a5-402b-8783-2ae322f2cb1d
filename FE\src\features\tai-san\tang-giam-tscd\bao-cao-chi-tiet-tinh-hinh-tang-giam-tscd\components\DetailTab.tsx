import { assetTypeSearchColumns, departmentSearchColumns, assetGroupSearchColumns } from '../cols-definition';
import { FormField } from '@/components/custom/arito/form/form-field';

export const DetailTab = ({ formMode }: { formMode: 'add' | 'edit' | 'view' }) => (
  <div className='p-4'>
    <div className='grid grid-cols-1 gap-x-8 space-y-2 lg:grid-cols-1 lg:space-y-0'>
      <div className='space-y-2'>
        {/* Loại tài sản */}
        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[140px,1fr]'
          type='text'
          label='Loại tài sản'
          name='ma_loai_tai_san'
          disabled={formMode === 'view'}
          withSearch={true}
          searchEndpoint='AssetType'
          searchColumns={assetTypeSearchColumns}
          defaultSearchColumn='loai_tai_san'
          displayRelatedField='ten_loai_tai_san'
        />

        {/* Bộ phận sử dụng */}
        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[140px,1fr]'
          type='text'
          label='Bộ phận sử dụng'
          name='ma_bo_phan'
          disabled={formMode === 'view'}
          withSearch={true}
          searchEndpoint='Department'
          searchColumns={departmentSearchColumns}
          defaultSearchColumn='ma_bo_phan'
          displayRelatedField='ten_bo_phan'
        />

        {/* Nhóm tài sản 1,2,3 */}
        <div className='grid grid-cols-1 gap-x-2 lg:grid lg:grid-cols-[1.5fr,1fr,1fr]'>
          <FormField
            className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[140px,1fr]'
            type='text'
            label='Nhóm tài sản'
            name='nhom_tai_san_1'
            disabled={formMode === 'view'}
            withSearch={true}
            searchEndpoint='AssetGroup'
            searchColumns={assetGroupSearchColumns}
            defaultSearchColumn='ma_nhom'
            displayRelatedField='ten_nhom'
          />
          <FormField
            className='w-[1fr] items-center'
            type='text'
            name='nhom_tai_san_2'
            disabled={formMode === 'view'}
            withSearch={true}
            searchEndpoint='AssetGroup'
            searchColumns={assetGroupSearchColumns}
            defaultSearchColumn='ma_nhom'
            displayRelatedField='ten_nhom'
          />
          <FormField
            className='w-[1fr] items-center'
            type='text'
            name='nhom_tai_san_3'
            disabled={formMode === 'view'}
            withSearch={true}
            searchEndpoint='AssetGroup'
            searchColumns={assetGroupSearchColumns}
            defaultSearchColumn='ma_nhom'
            displayRelatedField='ten_nhom'
          />
        </div>

        {/* Mẫu báo cáo */}
        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[140px,1fr]'
          type='select'
          label='Mẫu báo cáo'
          name='mau_bao_cao'
          disabled={formMode === 'view'}
          options={[
            { value: '0', label: 'Mẫu tiền chuẩn' },
            { value: '1', label: 'Mẫu ngoại tệ' }
          ]}
        />
      </div>
    </div>
  </div>
);
