import { useFormContext } from 'react-hook-form';
import React, { useState } from 'react';
import { boPhanSearchColumns, groupColumns, taiSanSearchColumns } from '@/constants/search-columns';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import QUERY_KEYS from '@/constants/query-keys';
import { Label } from '@/components/ui/label';
import type { <PERSON><PERSON><PERSON> } from '@/types/schemas';

// Define types for the search field selections
interface TaiSan {
  uuid: string;
  ma_ts: string;
  ten_ts: string;
}

interface LoaiTaiSan {
  uuid: string;
  loai_ts: string;
  ten_loai_ts: string;
}

const DetailsTab: React.FC = () => {
  // State for selected search field values
  const [selectedTaiSan, setSelectedTaiSan] = useState<TaiSan | null>(null);
  const [selectedLoaiTaiSan, setSelectedLoaiTaiSan] = useState<LoaiTaiSan | null>(null);
  const [selectedBo<PERSON>han, setSelectedBoPhan] = useState<BoPhan | null>(null);
  const [selectedNhom1, setSelectedNhom1] = useState<any | null>(null);
  const [selectedNhom2, setSelectedNhom2] = useState<any | null>(null);
  const [selectedNhom3, setSelectedNhom3] = useState<any | null>(null);

  // Get form context if needed for form submission
  const { setValue } = useFormContext();

  // Handle selection of search field items
  const handleTaiSanSelection = (item: TaiSan) => {
    setSelectedTaiSan(item);
    setValue('ma_ts', item.uuid);
  };

  const handleLoaiTaiSanSelection = (item: LoaiTaiSan) => {
    setSelectedLoaiTaiSan(item);
    setValue('loai_ts', item.uuid);
  };

  const handleBoPhanSelection = (item: BoPhan) => {
    setSelectedBoPhan(item);
    setValue('ma_bp_ts', item.uuid);
  };

  const handleNhom1Selection = (item: any) => {
    setSelectedNhom1(item);
    setValue('nh_ts1', item.uuid);
  };

  const handleNhom2Selection = (item: any) => {
    setSelectedNhom2(item);
    setValue('nh_ts2', item.uuid);
  };

  const handleNhom3Selection = (item: any) => {
    setSelectedNhom3(item);
    setValue('nh_ts3', item.uuid);
  };

  return (
    <div className='w-[800px] min-w-[800px] space-y-2 p-4'>
      <div className='flex flex-col space-y-3'>
        {/* Mã tài sản */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Mã tài sản</Label>
          <SearchField<TaiSan>
            searchEndpoint={`/${QUERY_KEYS.KHAI_BAO_THONG_TIN_TSCD}/`}
            searchColumns={taiSanSearchColumns}
            dialogTitle='Danh mục tài sản'
            columnDisplay='ma_ts'
            displayRelatedField='ten_ts'
            value={selectedTaiSan?.ma_ts || ''}
            onRowSelection={handleTaiSanSelection}
          />
          {/* Hidden field to store the UUID */}
          <FormField type='text' className='hidden' name='ma_ts' />
        </div>

        {/* Loại tài sản */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Loại tài sản</Label>
          <SearchField<LoaiTaiSan>
            searchEndpoint={`/${QUERY_KEYS.LOAI_TAI_SAN_CONG_CU}/`}
            searchColumns={[
              { field: 'loai_ts', headerName: 'Loại tài sản', width: 150 },
              { field: 'ten_loai_ts', headerName: 'Tên loại tài sản', width: 250 }
            ]}
            dialogTitle='Danh mục loại tài sản'
            columnDisplay='loai_ts'
            displayRelatedField='ten_loai_ts'
            value={selectedLoaiTaiSan?.loai_ts || ''}
            onRowSelection={handleLoaiTaiSanSelection}
          />
          {/* Hidden field to store the UUID */}
          <FormField type='text' className='hidden' name='loai_ts' />
        </div>

        {/* Bộ phận sử dụng */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Bộ phận sử dụng</Label>
          <SearchField<BoPhan>
            searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
            searchColumns={boPhanSearchColumns}
            dialogTitle='Danh mục bộ phận'
            columnDisplay='ma_bp'
            displayRelatedField='ten_bp'
            value={selectedBoPhan?.ma_bp || ''}
            onRowSelection={handleBoPhanSelection}
          />
          {/* Hidden field to store the UUID */}
          <FormField type='text' className='hidden' name='ma_bp_ts' />
        </div>

        {/* Nhóm tài sản 1,2,3 */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Nhóm tài sản 1,2,3</Label>
          <div className='flex gap-2'>
            <SearchField<any>
              searchEndpoint={`/${QUERY_KEYS.NHOM_TSCD}/`}
              searchColumns={groupColumns}
              dialogTitle='Danh mục nhóm tài sản 1'
              columnDisplay='ma_nhom'
              displayRelatedField='ten_nhom'
              value={selectedNhom1?.ma_nhom || ''}
              onRowSelection={handleNhom1Selection}
            />
            <SearchField<any>
              searchEndpoint={`/${QUERY_KEYS.NHOM_TSCD}/`}
              searchColumns={groupColumns}
              dialogTitle='Danh mục nhóm tài sản 2'
              columnDisplay='ma_nhom'
              displayRelatedField='ten_nhom'
              value={selectedNhom2?.ma_nhom || ''}
              onRowSelection={handleNhom2Selection}
            />
            <SearchField<any>
              searchEndpoint={`/${QUERY_KEYS.NHOM_TSCD}/`}
              searchColumns={groupColumns}
              dialogTitle='Danh mục nhóm tài sản 3'
              columnDisplay='ma_nhom'
              displayRelatedField='ten_nhom'
              value={selectedNhom3?.ma_nhom || ''}
              onRowSelection={handleNhom3Selection}
            />
          </div>
          {/* Hidden fields to store the UUIDs */}
          <FormField type='text' className='hidden' name='nh_ts1' />
          <FormField type='text' className='hidden' name='nh_ts2' />
          <FormField type='text' className='hidden' name='nh_ts3' />
        </div>

        {/* Mẫu báo cáo */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Mẫu báo cáo</Label>
          <FormField
            name='mau_bao_cao'
            type='select'
            options={[
              { value: 'TC', label: 'Mẫu tiêu chuẩn' },
              { value: 'NT', label: 'Mẫu ngoại tệ' }
            ]}
            className='w-full'
            defaultValue={'TC'}
          />
        </div>
      </div>
    </div>
  );
};

export default DetailsTab;
