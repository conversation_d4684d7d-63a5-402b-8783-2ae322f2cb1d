# Hóa Đơn <PERSON>a D<PERSON>ch Vụ - Purchase Service Invoice

## Overview
This module handles the purchase service invoice functionality with a comprehensive form interface for managing service purchase transactions.

## Key Components

### BasicInfoTab.tsx
The main form component with a 2-column layout:

**Left Column - Basic Information:**
- Transaction type (Radio buttons: "DV. Mua dịch vụ", "TS. Mua tài sản/công cụ")
- Payment and discount checkboxes
- Supplier information (code, name, address)
- Employee code
- Account information
- Tax code with action buttons
- Debt amount (displayed in red)
- Delivery person
- Email
- Payment terms

**Right Column - Document Information:**
- Document number with search functionality
- Document date
- Invoice number
- Invoice symbol
- Invoice date
- Foreign currency and exchange rate
- Status with toggle switch
- Data received checkbox

### schemas.ts
Contains form validation schema and initial values:
- Form data types and validation rules
- Default values matching the UI requirements
- Currency and transaction type options

## Recent Fixes

### Layout Issues Resolved
1. **Radio Button Spacing**: Fixed overlapping text in transaction type radio buttons
2. **Checkbox Layout**: Improved spacing between "<PERSON> tiền" and "Chiết khấu" checkboxes
3. **Grid Layout**: Restructured from complex 5-column grid to clean 2-column layout
4. **Element Spacing**: Added proper gaps and margins to prevent overlapping

### UI Improvements
- Consistent spacing with `gap-2` and `gap-4` classes
- Proper label alignment with grid layouts
- Clear visual separation between form sections
- Responsive design maintained

## Technical Implementation

### Form Structure
```tsx
<div className='p-4'>
  <div className='grid grid-cols-1 gap-x-8 lg:grid-cols-2'>
    {/* Left Column */}
    <div className='space-y-4'>
      {/* Form fields with consistent spacing */}
    </div>
    
    {/* Right Column */}
    <div className='space-y-4'>
      {/* Document fields */}
    </div>
  </div>
</div>
```

### Key Features
- **Search Integration**: Multiple fields with search functionality
- **Form Validation**: Yup schema validation
- **Default Values**: Pre-populated sample data
- **Responsive Design**: Mobile-friendly layout
- **Accessibility**: Proper labels and form associations

## Dependencies
- React Hook Form for form management
- Yup for validation
- Custom Arito components for UI consistency
- Radix UI for radio buttons and switches

## Usage
The component is used in the main page component with proper form initialization and schema validation.
