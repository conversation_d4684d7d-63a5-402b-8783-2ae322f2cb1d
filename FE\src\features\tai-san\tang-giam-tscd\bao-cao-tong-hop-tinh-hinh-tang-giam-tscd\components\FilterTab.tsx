import { FormField } from '@/components/custom/arito/form/form-field';

export const FilterTab = ({ formMode }: { formMode: 'add' | 'edit' | 'view' }) => (
  <div className='p-4'>
    <div className='grid grid-cols-1 gap-x-8 space-y-2 lg:grid-cols-1 lg:space-y-0'>
      <div className='space-y-2'>
        {/* Từ kỳ/năm row */}
        <div className='grid w-2/3 grid-cols-1 gap-x-8 lg:grid lg:grid-cols-2'>
          <FormField
            className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
            type='text'
            label='Từ kỳ/năm'
            name='tu_ky'
            disabled={formMode === 'view'}
          />
          <FormField
            className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
            type='text'
            name='tu_nam'
            disabled={formMode === 'view'}
          />
        </div>

        {/* Đến kỳ/năm row */}
        <div className='grid w-2/3 grid-cols-1 gap-x-8 lg:grid lg:grid-cols-2'>
          <FormField
            className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
            type='text'
            label='Đến kỳ/năm'
            name='den_ky'
            disabled={formMode === 'view'}
          />
          <FormField
            className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
            type='text'
            name='den_nam'
            disabled={formMode === 'view'}
          />
        </div>
      </div>
    </div>
  </div>
);
