fix(purchase-service-invoice): resolve overlapping UI elements in form layout

- Fixed radio button text overlapping in transaction type selection
- Improved spacing between "Chi tiền" and "Chiết khấu" checkboxes  
- Restructured form layout from complex 5-column to clean 2-column grid
- Added consistent spacing with gap-2 and gap-4 classes
- Enhanced visual separation between form sections
- Maintained responsive design for mobile compatibility

Changes:
- Updated BasicInfoTab.tsx with proper grid layout and spacing
- Added schemas.ts for form validation and initial values
- Integrated Switch component for status toggle
- Added sample data matching UI requirements
- Created comprehensive README documentation

The form now displays properly without overlapping elements and provides
a better user experience with clear visual hierarchy.
