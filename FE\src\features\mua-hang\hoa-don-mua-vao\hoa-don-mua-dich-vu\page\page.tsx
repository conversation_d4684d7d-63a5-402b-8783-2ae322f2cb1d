'use client';
import { GridRowParams } from '@mui/x-data-grid';
import { useState } from 'react';
import Split from 'react-split';
import { filter } from 'lodash';
import { exportBottomColumns, exportMainColumns } from '../cols-definition';
import { AritoInputTable } from '@/components/custom/arito/input-table';
import AritoColoredDot from '@/components/custom/arito/colored-dot';
import AritoDataTables from '@/components/custom/arito/data-tables';
import FromPopup from '../components/popup/from-popup/FromPopup';
import { BasicInfoTab } from '../components/BasicInfoTab';
import { AritoForm } from '@/components/custom/arito';
import { filterableCols } from '../types/filterCols';
import { ActionBar } from '../components/ActionBar';
import { BottomBar } from '../components/BottomBar';
import { DetailTab } from '../components/DetailTab';
import { calculateTotals } from '../utils/Caculate';
import { filterValues } from '../types/filterTabs';
import SearchForm from '../components/SearchForm';
import { OtherTab } from '../components/OtherTab';
import { TaxTab } from '../components/Tax';
import {
  purchaseServiceInvoiceSchema,
  purchaseServiceInvoiceInitialValues
} from '../schemas';

export default function ServicePurchaseInvoicePage() {
  const [showForm, setShowForm] = useState(false);
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('view');
  const [selectedObj, setSelectedObj] = useState<any | null>(null);
  const [currentObj, setCurrentObj] = useState<any | null>(null);
  const [inputDetails, setInputDetails] = useState<any[]>([]);
  const [rows, setRows] = useState<any[]>([]);
  const [showFromPopUpForm, setShowFromPopUpForm] = useState(false);
  const [showTaxCodePopupForm, setShowTaxCodePopupForm] = useState(false);
  const [showSearchForm, setShowSearchForm] = useState(false);

  const handleCloseForm = () => {
    setShowForm(false);
  };

  const handleOpenEditForm = (obj: any) => {
    setFormMode('edit');
    setCurrentObj(obj);
    setInputDetails(obj.details || []);
    setShowForm(true);
  };

  const handleOpenViewForm = (obj: any) => {
    setFormMode('view');
    setCurrentObj(obj);
    setInputDetails(obj.details || []);
    setShowForm(true);
  };

  const handleOpenAddForm = () => {
    setFormMode('add');
    setCurrentObj(purchaseServiceInvoiceInitialValues);
    setInputDetails([]);
    setShowForm(true);
  };

  const handleRowClick = (params: GridRowParams) => {
    const obj = params.row as any;
    setSelectedObj(obj);
    setInputDetails(obj.details || []);
  };

  const handleFormSubmit = async (data: any) => {};

  const handleSearchSubmit = (data: any) => {
    console.log('Search criteria:', data);
    // TODO: Implement actual search logic here

    // Mock search result
    const filteredRows = rows.filter(row => {
      // Apply search filters based on the data
      return true; // Replace with actual filter logic
    });

    setRows(filteredRows);
    setShowSearchForm(false);
  };

  const tables = [
    {
      name: 'Tất cả',
      rows: rows,
      columns: exportMainColumns(handleOpenViewForm, handleOpenEditForm)
    },
    ...filterValues.map(filterValue => {
      const filteredRows = filter(rows, row => row.status === filterValue.value);
      return {
        name: filterValue.name,
        rows: filteredRows,
        columns: exportMainColumns(handleOpenViewForm, handleOpenEditForm),
        icon: <AritoColoredDot color={filterValue.color} className='mr-2' />,
        tabProps: { className: 'whitespace-nowrap' }
      };
    }),
    ...(filterValues.length > 0 ? [] : [])
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {showForm ? (
        <div className='flex h-full flex-col'>
          <div className='flex-1 overflow-y-auto pb-[120px]'>
            <AritoForm
              mode={formMode}
              initialData={currentObj || purchaseServiceInvoiceInitialValues}
              schema={purchaseServiceInvoiceSchema}
              onSubmit={handleFormSubmit}
              onClose={handleCloseForm}
              subTitle='Hóa đơn mua dịch vụ'
              headerFields={
                <BasicInfoTab
                  formMode={formMode}
                  setShowTaxCodePopupForm={setShowTaxCodePopupForm}
                  showTaxCodePopupForm={showTaxCodePopupForm}
                />
              }
              tabs={[
                {
                  id: 'chi-tiet',
                  label: 'Chi tiết',
                  component: <DetailTab value={inputDetails} onChange={setInputDetails} formMode={formMode} />
                },
                {
                  id: 'thue',
                  label: 'Thuế',
                  component: <TaxTab value={inputDetails} onChange={setInputDetails} formMode={formMode} />
                },
                {
                  id: 'file_dinh_kem',
                  label: 'File đính kèm',
                  component: <OtherTab value={inputDetails} onChange={setInputDetails} formMode={formMode} />
                }
              ]}
            />
          </div>
          <div className='fixed bottom-0 left-0 right-0 bg-white'>
            <BottomBar
              totalQuantity={calculateTotals(inputDetails).totalQuantity}
              totalExpense={calculateTotals(inputDetails).totalExpense}
              totalTax={calculateTotals(inputDetails).totalTax}
              totalAmount={calculateTotals(inputDetails).totalAmount}
              totalPayment={0}
              formMode={formMode}
            />
          </div>
        </div>
      ) : (
        <>
          <ActionBar
            onAddClick={handleOpenAddForm}
            onEditClick={() => selectedObj && handleOpenEditForm(selectedObj)}
            onDeleteClick={() => {}}
            onCopyClick={() => {}}
            onSearchClick={() => setShowSearchForm(true)}
            onRefreshClick={() => {}}
            onPrintClick={() => {}}
          />
          <Split
            className='flex flex-1 flex-col overflow-hidden'
            direction='vertical'
            sizes={[50, 50]}
            minSize={200}
            gutterSize={8}
            gutterAlign='center'
            snapOffset={30}
            dragInterval={1}
            cursor='row-resize'
          >
            <div className='w-full overflow-hidden'>
              <AritoDataTables
                tables={tables}
                onRowClick={handleRowClick}
                selectedRowId={selectedObj?.id || undefined}
                // filterableColumns={filterableCols}
              />
            </div>
            <div className='w-full overflow-hidden'>
              <AritoInputTable
                value={selectedObj?.details || []}
                columns={exportBottomColumns}
                tableActionButtons={['export', 'pin']}
                mode='view'
              />
            </div>
          </Split>
        </>
      )}
      {showFromPopUpForm && (
        <FromPopup
          showFromPopUpForm={showFromPopUpForm}
          setShowFromPopUpForm={setShowFromPopUpForm}
          formMode={formMode}
          currentObj={currentObj}
        />
      )}
      {showSearchForm && (
        <SearchForm open={showSearchForm} onClose={() => setShowSearchForm(false)} onSubmit={handleSearchSubmit} />
      )}
    </div>
  );
}
