'use client';

import { taiSanSearchColumns, loaiTaiSanSearchColumns, QUERY_KEYS } from '@/constants';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { RadioButton } from '@/components/custom/arito/form/radio-button';
import NumberRangeDropdown from '../form-fields/number-range-dropdown';
import { FormField } from '@/components/custom/arito/form/form-field';
import { LoaiTSCDCCDC, TaiSanCoDinh } from '@/types/schemas';
import { Label } from '@/components/ui/label';

interface BasicInfoTabProps {
  setAssetType: (assetType: LoaiTSCDCCDC) => void;
  setAssetCode: (assetCode: TaiSanCoDinh) => void;
}

export const BasicInfoTab: React.FC<BasicInfoTabProps> = ({ setAssetType, setAssetCode }) => {
  return (
    <div className='max-h-[calc(100vh-150px)] w-[800px] min-w-[800px] overflow-y-auto'>
      <div className='space-y-2 p-4'>
        <div className='flex flex-col space-y-6'>
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Kỳ từ/đến</Label>
            <NumberRangeDropdown fromName='from' toName='to' />
          </div>

          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Năm</Label>
            <FormField className='w-20 min-w-[100px]' type='number' name='year' />
          </div>

          {/* Asset Code Search Field */}
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Mã tài sản</Label>
            <SearchField<TaiSanCoDinh>
              type='text'
              columnDisplay='ma_ts'
              displayRelatedField='ten_ts'
              searchEndpoint={`/${QUERY_KEYS.KHAI_BAO_THONG_TIN_TSCD}`}
              searchColumns={taiSanSearchColumns}
              className='w-full min-w-[300px]'
              onRowSelection={setAssetCode}
            />
          </div>

          {/* Asset Type Search Field */}
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Loại tài sản</Label>
            <SearchField<LoaiTSCDCCDC>
              type='text'
              columnDisplay='ma_lts'
              displayRelatedField='ten_lts'
              searchEndpoint={`/${QUERY_KEYS.LOAI_TAI_SAN_CONG_CU}`}
              searchColumns={loaiTaiSanSearchColumns}
              className='w-full min-w-[300px]'
              onRowSelection={setAssetType}
            />
          </div>

          <div className='flex'>
            <Label className='w-40 min-w-40'>Diễn giải bút toán</Label>
            <div className='flex flex-col'>
              <Label className='mb-2 text-gray-500'>Bút toán phân bổ TSCD kỳ [MM] năm [YYYY] tài sản [TS]</Label>
              <div className='mb-2 h-[1px] bg-gray-200' />
              <RadioButton
                name='action'
                options={[
                  { value: 'cost', label: 'Xem chi phí' },
                  { value: 'calculate', label: 'Tính phân bổ và hạch toán TSCD' },
                  { value: 'delete', label: 'Xoá phân bổ' }
                ]}
                defaultValue='calculate'
                orientation='horizontal'
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
