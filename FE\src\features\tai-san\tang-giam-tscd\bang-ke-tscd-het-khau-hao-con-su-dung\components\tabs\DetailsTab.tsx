import { useFormContext } from 'react-hook-form';
import React from 'react';
import { boPhanSearchColumns, groupColumns, loaiTaiSanSearchColumns } from '@/constants';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { QUERY_KEYS } from '@/constants/query-keys';
import { Label } from '@/components/ui/label';

const DetailsTab: React.FC = () => {
  const { setValue } = useFormContext();

  return (
    <div className='w-[800px] min-w-[800px] space-y-4 p-4'>
      <div className='flex flex-col space-y-3'>
        {/* 1. Loại tài sản */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Loại tài sản:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.LOAI_TAI_SAN_CONG_CU}/`}
              searchColumns={loaiTaiSanSearchColumns}
              dialogTitle='Danh mục loại tài sản'
              columnDisplay='loai_tai_san'
              displayRelatedField='ten_loai'
              onValueChange={value => {
                setValue('loai_tai_san', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('loai_tai_san', row.loai_tai_san);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* 2. Bộ phận sử dụng */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Bộ phận sử dụng:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
              searchColumns={boPhanSearchColumns}
              dialogTitle='Danh mục bộ phận'
              columnDisplay='ma_bp'
              displayRelatedField='ten_bp'
              onValueChange={value => {
                setValue('bo_phan_su_dung', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('bo_phan_su_dung', row.ma_bp);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* 3. Nhóm tài sản */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Nhóm tài sản:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.NHOM_TSCD}/`}
              searchColumns={groupColumns}
              dialogTitle='Danh mục nhóm tài sản'
              columnDisplay='ma_nhom'
              displayRelatedField='ten_nhom'
              onValueChange={value => {
                setValue('nhom_tai_san', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('nhom_tai_san', row.ma_nhom);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* 4. Mẫu báo cáo */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mẫu báo cáo:</Label>
          <div className='w-64'>
            <FormField
              name='mau_bao_cao'
              type='select'
              options={[
                { value: '0', label: 'Mẫu tiền chuẩn' },
                { value: '1', label: 'Mẫu ngoại tệ' }
              ]}
              className='w-full'
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailsTab;
